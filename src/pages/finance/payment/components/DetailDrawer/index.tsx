import LeftTitle from '@/components/LeftTitle';
import { ObjectType } from '@/components/ProFormItem/ProFormObject';
import AuthButton from '@/components/common/AuthButton';
import FunProTable from '@/components/common/FunProTable';
import { queryOtherRelatedCompanyDetail } from '@/pages/finance/otherRelated/service';
import { queryPayableDetailPage } from '@/pages/finance/payment/services';
import type { FinPayableDetailModalType } from '@/pages/finance/payment/types/FinPayableDetailModalType';
import type { FinPayableEntity } from '@/pages/finance/payment/types/FinPayableEntity';
import { queryFullById } from '@/pages/purchase/supplier/services';
import { exportData } from '@/utils/exportData';
import { DrawerForm, ProCard, ProDescriptions } from '@ant-design/pro-components';
import type { ActionType } from '@ant-design/pro-table/lib';
import { useIntl } from '@umijs/max';
import { useAsyncEffect } from 'ahooks';
import { Checkbox, Flex, Space } from 'antd';
import { useRef, useState } from 'react';
import { getDetailColumns } from '../../config/DetailColumns';
import { OrderAmountList, PayableAmountList, PaymentAmountList } from '../../types/FinPayableGroupEntity';

export default (props: FinPayableDetailModalType) => {
  const intl = useIntl();
  const [orderAmount, setOrderAmount] = useState<OrderAmountList[]>([]);
  const [payableAmount, setPayableAmount] = useState<PayableAmountList[]>([]);
  const [payedAmount, setPayedAmount] = useState<PaymentAmountList[]>([]);
  const [supplierName, setSupplierName] = useState<string>('0');
  const [payableFlag, setPayableFlag] = useState(false);

  const actionRef = useRef<ActionType>();
  useAsyncEffect(async () => {
    actionRef.current?.reload(true);
  }, [payableFlag]);

  useAsyncEffect(async () => {
    await setPayableFlag(false);
  }, [props.visible]);

  /**
   * 分页查询数据
   */
  const queryPayableDetailPageQuery = async ({ current = 1, pageSize = 10 }) => {
    if (!props.record) {
      return;
    }
    const queryResult = await queryPayableDetailPage({
      ...props.record,
      pageNo: current,
      pageSize,
      storeIdList: props.record.storeId ? [props.record.storeId] : [],
      payableFlag: payableFlag ? 1 : 0,
    });
    if (!queryResult || !queryResult.data) {
      setOrderAmount([]);
      setPayableAmount([]);
      setPayedAmount([]);
      return [];
    }
    setOrderAmount(queryResult?.data?.[0].orderAmountList);
    setPayableAmount(queryResult?.data?.[0].payableAmountList);
    setPayedAmount(queryResult?.data?.[0].paymentAmountList);
    return { ...queryResult, data: queryResult.data?.[0].detailRoList };
  };

  return (
    <DrawerForm
      title={props.title}
      width={1080}
      drawerProps={{
        classNames: {
          body: 'bg-[#f2f2f2]',
        },
        maskClosable: false,
        onClose: props.onCancel,
      }}
      open={props.visible}
      submitter={false}
    >
      <ProCard className="mb-4">
        <ProDescriptions
          key={props.record?.buyerId + props.record?.storeName}
          title={supplierName}
          column={3}
          request={async () => {
            if (!props.record?.sellerId) {
              return null;
            }
            if (!props.record?.sellerType === ObjectType.Suppler) {
              const supplier = await queryFullById({ id: props.record?.sellerId || '' });
              setSupplierName(supplier?.supplierInfo?.supplierName || '');
              let defaultContacts = supplier?.supplierConcatList?.filter(
                (item) => item.isDefault === 1,
              )?.[0];
              if (!defaultContacts) {
                defaultContacts = supplier?.supplierConcatList?.[0];
              }
              let defaultAddress = supplier?.supplierAddressList?.filter(
                (item) => item.isDefault === 1,
              )?.[0];
              if (!defaultAddress) {
                defaultAddress = supplier?.supplierAddressList?.[0];
              }

              return Promise.resolve({
                success: true,
                data: {
                  storeName: props.record?.storeName,
                  supplierCode: supplier?.supplierInfo?.supplierCode,
                  concatPerson: `${defaultContacts?.concatPerson ?? ''}`,
                  concatPhone: `${defaultContacts?.concatPhone ?? ''}`,
                  contactsAddress: `${defaultAddress?.province ?? ''}${defaultAddress?.city ?? ''}${defaultAddress?.area ?? ''
                    }${defaultAddress?.detailAddress ?? ''}`.trim(),
                },
              });
            }

            if (props.record?.sellerType === ObjectType.OtherCompany) {
              const detail = await queryOtherRelatedCompanyDetail({ id: props.record?.sellerId || '' })
              setSupplierName(detail?.otherRelatedCompanyInfo?.companyName || '');
              let defaultContacts = detail?.otherRelatedCompanyConcatList?.filter(item => item.isDefault === 1)?.[0];
              if (!defaultContacts) {
                defaultContacts = detail?.otherRelatedCompanyConcatList?.[0];
              }
              let defaultAddress = detail?.otherRelatedCompanyAddressList?.filter(item => item.isDefault === 1)?.[0];
              if (!defaultAddress) {
                defaultAddress = detail?.otherRelatedCompanyAddressList?.[0];
              }

              return Promise.resolve({
                success: true,
                data: {
                  storeName: props.record?.storeName,
                  supplierCode: detail?.otherRelatedCompanyInfo?.companyCode,
                  concatPerson: `${defaultContacts?.concatFirstName ?? ''} ${defaultContacts?.concatLastName ?? ''}`,
                  concatPhone: `${defaultContacts?.concatPhone ?? ''}`,
                  contactsAddress: `${defaultAddress?.province ?? ''}${defaultAddress?.area ?? ''}${defaultAddress?.detailAddress ?? ''}`.trim(),
                },
              });
            }
          }}
          columns={[
            {
              title: intl.formatMessage({ id: 'finance.payment.columns.store' }),
              dataIndex: 'storeName',
            },
            {
              title: intl.formatMessage({ id: 'finance.payment.columns.supplierCode' }),
              dataIndex: 'supplierCode',
            },
            {
              title: intl.formatMessage({ id: 'finance.payment.columns.contact' }),
              dataIndex: 'concatPerson',
            },
            {
              title: intl.formatMessage({ id: 'finance.payment.columns.contactPhone' }),
              dataIndex: 'concatPhone',
            },
            {
              title: intl.formatMessage({ id: 'finance.payment.columns.contactAddress' }),
              dataIndex: 'contactsAddress',
            },
          ]}
        />
      </ProCard>
      <FunProTable<FinPayableEntity, any>
        actionRef={actionRef}
        scroll={{ x: 'max-content' }}
        key={props.record?.buyerId + props.record?.storeId + props.record?.status}
        title={() => (
          <Flex justify="space-between" align="flex-end">
            <Space direction="vertical" size={16}>
              <LeftTitle title={intl.formatMessage({ id: 'finance.payment.detailTitle' })} />
              <AuthButton
                className=""
                authority="exportPayableDetail"
                type="primary"
                ghost
                onClick={() => {
                  exportData({
                    systemId: 'ETC_SAAS_SYS',
                    taskDesc: intl.formatMessage({ id: 'finance.payment.exportDetailDescription' }),
                    moduleId: 'PAYABLE_DETAIL_EXPORT',
                    params: {
                      ...props.record,
                      payableFlag: payableFlag ? 1 : 0,
                      storeIdList: props.record?.storeId ? [props.record.storeId] : [],
                    },
                  });
                  props.onCancel?.();
                }}
              >
                {intl.formatMessage({ id: 'common.button.export' })}
              </AuthButton>
            </Space>
            <Checkbox onChange={(e) => setPayableFlag(e.target.checked)}>
              {intl.formatMessage({ id: 'finance.payment.onlyShowRemaining' })}
            </Checkbox>
          </Flex>
        )}
        search={false}
        options={false}
        columns={getDetailColumns(intl)}
        request={queryPayableDetailPageQuery}
      />
      <ProCard bordered>
        <div className="flex justify-end">
          <div>
            <Space direction="vertical" className='mr-2' >
              <span className="text-[16px] font-semibold text-[#000000D9]">
                {intl.formatMessage({ id: 'finance.payment.summary.orderTotal' })}：
              </span>
              <span className="text-[16px] font-semibold text-[#000000D9]">
                {intl.formatMessage({ id: 'finance.payment.summary.paidTotal' })}：
              </span>
              <span className="text-[16px] font-semibold text-[#000000D9] leading-[40px]">
                {intl.formatMessage({ id: 'finance.payment.summary.payableTotal' })}：
              </span>
            </Space>
            <Space direction="vertical" className='text-right'>
              <span className="text-[16px] font-semibold text-[#000000D9]">
                {
                  orderAmount.map((item, index) => (
                    <span key={item.currency}>
                      {item.currencySymbol}{item.amount?.toFixed(2)}
                      {index !== orderAmount.length - 1 ? ';' : ''}
                    </span>
                  ))
                }
              </span>
              <span className="text-[16px] font-semibold text-[#000000D9]">
                {
                  payedAmount.map((item, index) => (
                    <span key={item.currency}>
                      {item.currencySymbol}{item.amount?.toFixed(2)}
                      {index !== payedAmount.length - 1 ? ';' : ''}
                    </span>
                  ))
                }
              </span>
              <span className="text-[24px] font-medium text-primary">
                {
                  payableAmount.map((item, index) => (
                    <span key={item.currency}>
                      {item.currencySymbol}{item.amount?.toFixed(2)}
                      {index !== payableAmount.length - 1 ? ';' : ''}
                    </span>
                  ))
                }
              </span>
            </Space>
          </div>
        </div>
      </ProCard>
    </DrawerForm>
  );
};
