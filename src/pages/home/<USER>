import LeftTitle from '@/components/LeftTitle';
import MessageList from '@/pages/home/<USER>/MessageList';
import { queryStoreByAccount } from '@/pages/personnel/user/services';
import {
  DownOutlined
} from '@ant-design/icons';
import { PageContainer, ProCard } from '@ant-design/pro-components';
import { history, useIntl, useModel } from '@umijs/max';
import { useAsyncEffect, useInterval, useReactive } from 'ahooks';
import type { MenuProps } from 'antd';
import { Dropdown, Flex, Image, Space, message } from 'antd';
import { defaultTo, find, isEmpty } from 'lodash';
import { useState } from 'react';
import { queryFinReceivablePage } from '../finance/collection/services';
import { queryOrderStatusCount } from '../sales/order/list/services';
import { OrderStatus } from '../sales/order/list/types/OrderStatus';
import { queryInventoryPagePost } from '../stocks/inventory/services';
import HomeTodoList from './components/TodoList';
import { CommonFunctions, TodoList } from './config';
import type { StoreRequestParams } from './types/StoreRequestParams';
// These will be moved inside the component to access intl
type SalesViewType = { key: string; title: string; todayValue: string; yesterdayValue: string };
type SalesTrendLineType = { date: string; value: number | null; type: string };
type SalesTrendBarType = { type: string; date: string; value: number };

import withKeepAlive from '@/wrappers/withKeepAlive';
import phoneFrame from '../../assets/icons/home/<USER>';
import qrcode from '../../assets/icons/home/<USER>';

const Home = () => {
  const intl = useIntl();
  const { initialState } = useModel('@@initialState');
  // const [dataType, setDataType] = useState<'DAY' | 'MONTH'>('DAY');
  // const [state, { toggle }] = useToggle(true);
  // const access = useAccess();

  // Internationalized constants
  const AllStoreItem = { key: '0', label: intl.formatMessage({ id: 'home.store.all' }) };
  // const DataTypeTips = {
  //   DAY: intl.formatMessage({ id: 'home.overview.yesterday' }),
  //   MONTH: intl.formatMessage({ id: 'home.overview.lastMonth' }),
  // };

  // const TitleMap: Record<string, string> = {
  //   saleAmount: intl.formatMessage({ id: 'home.overview.saleAmount' }),
  //   saleGrossProfit: intl.formatMessage({ id: 'home.overview.saleGrossProfit' }),
  //   saleOrderNum: intl.formatMessage({ id: 'home.overview.saleOrderNum' }),
  //   fundsReceived: intl.formatMessage({ id: 'home.overview.fundsReceived' }),
  // };

  // const [viewList, setViewList] = useState<SalesViewType[]>([
  //   {
  //     key: 'saleAmount',
  //     title: intl.formatMessage({ id: 'home.overview.saleAmount' }),
  //     todayValue: '-',
  //     yesterdayValue: '-',
  //   },
  //   {
  //     key: 'saleGrossProfit',
  //     title: intl.formatMessage({ id: 'home.overview.saleGrossProfit' }),
  //     todayValue: '-',
  //     yesterdayValue: '-',
  //   },
  //   {
  //     key: 'saleOrderNum',
  //     title: intl.formatMessage({ id: 'home.overview.saleOrderNum' }),
  //     todayValue: '-',
  //     yesterdayValue: '-',
  //   },
  //   {
  //     key: 'fundsReceived',
  //     title: intl.formatMessage({ id: 'home.overview.fundsReceived' }),
  //     todayValue: '-',
  //     yesterdayValue: '-',
  //   },
  // ]);
  // const [updateTime, setUpdateTime] = useState<string>('-');
  const [storeIdListParams, setStoreIdListParams] = useState<StoreRequestParams>();

  const [count, setCount] = useState(0);
  // 每10分钟刷新一下
  useInterval(() => {
    setCount(count + 1);
  }, 1000 * 60 * 10);

  /**
   * 销售额
   */
  // const [saleAmountLineData, setSaleAmountLineData] = useState<SalesTrendLineType[]>([]);
  /**
   * 销售毛利
   */
  // const [saleGrossLineData, setSaleGrossLineData] = useState<SalesTrendLineType[]>([]);
  // const [saleBarData, setSaleBarData] = useState<SalesTrendBarType[]>([]);
  const [items, setItems] = useState<{ key: string; label: string }[]>([]);
  const [storeId, setStoreId] = useState<string>();
  const [storeName, setStoreName] = useState<string>('');

  // 查询门店列表
  useAsyncEffect(async () => {
    const result = await queryStoreByAccount({ status: 1 });
    if (result) {
      const storeList = [AllStoreItem, ...result.map((t) => ({ label: t.name, key: t.id }))];
      setItems(storeList);
      const selectItem = storeList[0];
      console.log(selectItem);
      setStoreName(selectItem?.label);
      setStoreId(selectItem?.key);
    }
  }, []);

  // 实时概况
  // useAsyncEffect(async () => {
  //   if (!storeId) {
  //     return;
  //   }
  //   const result = await queryIndexOverviewList({
  //     type: dataType,
  //     ...storeIdListParams,
  //   });
  //   if (isEmpty(result)) return;
  //   const viewObjList: SalesViewType[] = [];
  //   const updateTimeObj = find(result, (t) => !isEmpty(t.updateTime));
  //   setUpdateTime(defaultTo(updateTimeObj?.updateTime, '-'));
  //   const [yesterday, today] = result;
  //   forIn(TitleMap, (value, key) => {
  //     viewObjList.push({
  //       key,
  //       title: value,
  //       todayValue: defaultTo(today?.[key], '-'),
  //       yesterdayValue: defaultTo(yesterday?.[key], '-'),
  //     });
  //   });
  //   setViewList(viewObjList);
  // }, [dataType, storeId, count]);

  // 待办事项
  const todoMap = useReactive<Record<string, number>>({
    // 待处理订单
    WillHandle: 0,
    // 待出库订单
    OutBound: 0,
    // 库存预警
    StockWarning: 0,
    // 应收预警
    CollectWarning: 0,
  });

  // 待出库
  useAsyncEffect(async () => {
    if (!storeId) {
      return;
    }
    const result = await queryOrderStatusCount({
      ...storeIdListParams,
    });
    const res = result as Record<OrderStatus, number>;
    todoMap.OutBound = defaultTo(res[OrderStatus.WAIT_TO_OUTBOUND], 0);
    todoMap.WillHandle = defaultTo(res[OrderStatus.WAIT_TO_HANDLE], 0);
  }, [storeId, count]);

  // 库存预警
  useAsyncEffect(async () => {
    if (!storeId) {
      return;
    }
    const result = await queryInventoryPagePost({
      onlyTotalStatistics: true,
      invLimitStatusList: [1, 2],
      storeId: storeId == '0' ? undefined : storeId,
    });
    todoMap.StockWarning = defaultTo(result?.total, 0);
  }, [storeId, count]);

  // 应收预警
  useAsyncEffect(async () => {
    if (!storeId) {
      return;
    }
    const result = await queryFinReceivablePage({
      receivableFlag: 1,
      operatorNo: initialState?.currentUser?.accountId,
      ...storeIdListParams,
    });
    todoMap.CollectWarning = defaultTo(result?.total, 0);
  }, [storeId, count]);

  // 销售趋势
  // useAsyncEffect(async () => {
  //   if (!storeId) {
  //     return;
  //   }
  //   const result = await queryIndexSaleTrendList({
  //     ...storeIdListParams,
  //   });
  //   if (!isEmpty(result)) {
  //     const saleAmountlineData: SalesTrendLineType[] = [];
  //     const saleGrosslineData: SalesTrendLineType[] = [];
  //     const barData: SalesTrendBarType[] = [];
  //     forEach(result, (item) => {
  //       const { saleDate, saleGrossProfit, saleAmount, saleOrderNum } = item;
  //       const date = dayjs(saleDate).format('MM-DD');
  //       saleAmountlineData.push({
  //         type: intl.formatMessage({ id: 'home.trend.saleAmount' }),
  //         value: saleAmount ? Number(saleAmount) : 0,
  //         date,
  //       });
  //       saleGrosslineData.push({
  //         type: intl.formatMessage({ id: 'home.trend.saleGrossProfit' }),
  //         value: saleGrossProfit ? Number(saleGrossProfit) : null,
  //         date,
  //       });
  //       barData.push({
  //         type: intl.formatMessage({ id: 'home.trend.saleOrderNum' }),
  //         value: saleOrderNum ? Number(saleOrderNum) : 0,
  //         date,
  //       });
  //     });
  //     setSaleAmountLineData(saleAmountlineData);
  //     // 如果有空值则隐藏销售毛利
  //     if (saleGrosslineData.find((t) => isNil(t.value))) {
  //       setSaleGrossLineData([]);
  //     } else {
  //       setSaleGrossLineData(saleGrosslineData);
  //     }
  //     setSaleBarData(barData);
  //   }
  // }, [storeId, count]);

  // const typeDayClassName = classNames('select-none rounded cursor-pointer px-4 py-1 text-[12px]', {
  //   'bg-[#FFEDEDFF] text-[#F83331FF]': dataType == 'DAY',
  //   'bg-[#00000008] text-[#000000D9]': dataType == 'MONTH',
  // });
  // const typeMonthClassName = classNames(
  //   'select-none rounded cursor-pointer px-4 py-1 text-[12px]',
  //   {
  //     'bg-[#FFEDEDFF] text-[#F83331FF]': dataType == 'MONTH',
  //     'bg-[#00000008] text-[#000000D9]': dataType == 'DAY',
  //   },
  // );

  const onClick: MenuProps['onClick'] = ({ key }) => {
    const selectItem = find(items, ['key', key]);
    if (selectItem) {
      let storeIdList = undefined;
      if (key !== '0') {
        storeIdList = [key];
      }
      setStoreName(selectItem.label);
      setStoreId(key);
      setStoreIdListParams({ storeIdList });
    }
  };

  // const config: DualAxesConfig = {
  //   legend: {
  //     color: {
  //       layout: {
  //         justifyContent: 'center',
  //         alignItems: 'center',
  //       },
  //       // itemLabelText: (_, index) => labelArray[index],
  //     },
  //   },
  //   yField: 'value',
  //   xField: 'date',
  //   children: [
  //     {
  //       seriesField: 'type',
  //       colorField: 'type',
  //       data: [...saleAmountLineData, ...saleGrossLineData],
  //       type: 'line',
  //       shapeField: 'smooth',
  //       axis: {
  //         y: { position: 'left', title: intl.formatMessage({ id: 'home.trend.unit.yuan' }) },
  //       },
  //     },
  //     {
  //       seriesField: 'type',
  //       colorField: 'type',
  //       data: saleBarData,
  //       type: 'interval',
  //       style: { maxWidth: 40 },
  //       axis: {
  //         y: {
  //           position: 'right',
  //           title: intl.formatMessage({ id: 'home.trend.unit.order' }),
  //           titlePosition: 'top',
  //         },
  //       },
  //     },
  //   ],
  // };

  return (
    <PageContainer>
      <div className="flex">
        <Space direction={'vertical'} size={[0, 16]} className="flex-1">
          <ProCard bordered={false} headerBordered>
            <Dropdown
              className="text-[#000000D9] text-[20px] font-semibold"
              menu={{ items, onClick }}
            >
              <Space>
                <span className="text-[#000000D9] text-[20px] font-semibold">{storeName}</span>
                {!isEmpty(items) && <DownOutlined className="text-[#********]" />}
              </Space>
            </Dropdown>
            {/* 实时概况 */}
            {/*{access.hasButtonPerms('HomeRealtimeOverview') && (*/}
            {/*  <>*/}
            {/*    <Flex justify="space-between" align="center">*/}
            {/*      <Space>*/}
            {/*        <LeftTitle title={intl.formatMessage({ id: 'home.overview.title' })} />*/}
            {/*        /!* {viewList && ( *!/*/}
            {/*        <>*/}
            {/*          <span className="text-[#********] ml-3">*/}
            {/*            {intl.formatMessage({ id: 'home.overview.updateTime' })}：{updateTime}*/}
            {/*          </span>*/}
            {/*          {!state ? (*/}
            {/*            <EyeInvisibleOutlined*/}
            {/*              onClick={toggle}*/}
            {/*              className="cursor-pointer"*/}
            {/*              style={{ color: '#********' }}*/}
            {/*            />*/}
            {/*          ) : (*/}
            {/*            <EyeOutlined*/}
            {/*              onClick={toggle}*/}
            {/*              className="cursor-pointer"*/}
            {/*              style={{ color: '#********' }}*/}
            {/*            />*/}
            {/*          )}*/}
            {/*        </>*/}
            {/*        /!*)}*!/*/}
            {/*      </Space>*/}
            {/*      <Space size={12}>*/}
            {/*        <span className={typeDayClassName} onClick={() => setDataType('DAY')}>*/}
            {/*          {intl.formatMessage({ id: 'home.overview.today' })}*/}
            {/*        </span>*/}
            {/*        <span className={typeMonthClassName} onClick={() => setDataType('MONTH')}>*/}
            {/*          {intl.formatMessage({ id: 'home.overview.thisMonth' })}*/}
            {/*        </span>*/}
            {/*      </Space>*/}
            {/*    </Flex>*/}
            {/*    <Flex className="mt-6 px-6" gap={16} justify="space-between">*/}
            {/*      {viewList &&*/}
            {/*        viewList.map((viewItem) => (*/}
            {/*          <div key={viewItem.title} className="flex flex-col">*/}
            {/*            <Tooltip title={viewItem.title}>*/}
            {/*              <span className="text-[#000000D9]">*/}
            {/*                <span className="mr-2">{viewItem.title}</span>*/}
            {/*                <QuestionCircleOutlined style={{ fontSize: 14 }} />*/}
            {/*              </span>*/}
            {/*            </Tooltip>*/}
            {/*            <span className="text-[#000000D9] text-[24px] font-semibold mt-1 mb-2 w-40">*/}
            {/*              {state ? (*/}
            {/*                <span*/}
            {/*                  className="cursor-pointer"*/}
            {/*                  onClick={() => {*/}
            {/*                    switch (viewItem.key) {*/}
            {/*                      case 'fundsReceived':*/}
            {/*                        history.push('/finance/flow');*/}
            {/*                        break;*/}
            {/*                      default:*/}
            {/*                        history.push('/report/sales');*/}
            {/*                    }*/}
            {/*                  }}*/}
            {/*                >*/}
            {/*                  <NumberFormatText*/}
            {/*                    precision={viewItem.key == 'saleOrderNum' ? 0 : 2}*/}
            {/*                    numberText={viewItem.todayValue}*/}
            {/*                  />*/}
            {/*                </span>*/}
            {/*              ) : (*/}
            {/*                '***'*/}
            {/*              )}*/}
            {/*            </span>*/}
            {/*            <Space className="text-[#********]" size={4}>*/}
            {/*              {DataTypeTips[dataType]}*/}
            {/*              {state ? (*/}
            {/*                <NumberFormatText*/}
            {/*                  precision={viewItem.key == 'saleOrderNum' ? 0 : 2}*/}
            {/*                  numberText={viewItem.yesterdayValue}*/}
            {/*                />*/}
            {/*              ) : (*/}
            {/*                '***'*/}
            {/*              )}*/}
            {/*            </Space>*/}
            {/*          </div>*/}
            {/*        ))}*/}
            {/*    </Flex>*/}
            {/*  </>*/}
            {/*)}*/}
          </ProCard>
          {/* 常用功能 */}
          <ProCard bordered={false}>
            <LeftTitle title={intl.formatMessage({ id: 'home.functions.title' })} />
            <Flex className="mt-6 px-[16px]" gap={16} justify="space-between">
              {CommonFunctions.map((t) => (
                <span
                  key={t.key}
                  className="py-[8px] rounded-lg min-w-[80px] flex flex-col items-center cursor-pointer hover:bg-[#00000008]"
                  onClick={() => {
                    if (t?.tips) {
                      message.warning(t.tips);
                    } else {
                      history.push(t.href);
                    }
                  }}
                >
                  <Image preview={false} height={t.size[1]} width={t.size[0]} src={t.icon} />
                  <span className="mt-2 text-[#000000D9]">
                    {intl.formatMessage({ id: t.labelKey })}
                  </span>
                </span>
              ))}
            </Flex>
          </ProCard>
          {/* 待办事项 */}
          <ProCard bordered={false}>
            <LeftTitle title={intl.formatMessage({ id: 'home.todo.title' })} />
            <Flex className="mt-6 px-6" gap={16} justify="space-between">
              {TodoList.map((t) => (
                <Space
                  size={16}
                  key={t.key}
                  className="cursor-pointer"
                  onClick={() => {
                    let pathState: any = {};
                    switch (t.key) {
                      case 'WillHandle':
                        pathState = { ...storeIdListParams };
                        break;
                      case 'OutBound':
                        pathState = { ...storeIdListParams };
                        break;
                      case 'StockWarning':
                        pathState = { ...storeIdListParams };
                        break;
                      case 'CollectWarning':
                        pathState.operatorNo = initialState?.currentUser?.accountId;
                        pathState = { ...pathState, ...storeIdListParams };
                        break;

                      default:
                        break;
                    }
                    history.push(t.href, { ...pathState, ...t.state });
                  }}
                >
                  <div className="bg-[#FFF4F4FF] rounded-lg w-[48px] h-[48px] flex justify-center items-center">
                    <Image preview={false} src={t.icon} width={t.size[0]} height={t.size[1]} />
                  </div>
                  <div className="flex flex-col h-[60px] justify-between">
                    <span className="text-[#********]">
                      {intl.formatMessage({ id: t.labelKey })}
                    </span>
                    <span className="text-[#000000D9] text-[24px] font-semibold hover:text-[#F83431FF]">
                      {todoMap[t.key]}
                    </span>
                  </div>
                </Space>
              ))}
            </Flex>
          </ProCard>
          {/*{access.hasButtonPerms('HomeSalesTrend') && (*/}
          {/*  <ProCard bordered={false}>*/}
          {/*    <LeftTitle title={intl.formatMessage({ id: 'home.trend.title' })} />*/}
          {/*    /!* {!isEmpty(saleLineData) && !isEmpty(saleBarData) && *!/*/}
          {/*    <DualAxes {...config} />*/}
          {/*    /!* } *!/*/}
          {/*  </ProCard>*/}
          {/*)}*/}
        </Space>
        <Space direction={'vertical'} size={[0, 16]} className="w-[350px] flex-shrink-0 ml-[16px]">
          <MessageList />
          <HomeTodoList />
          <ProCard headerBordered={false}>
            <LeftTitle title={intl.formatMessage({ id: 'home.mobile.title' })} />
            <Space align="center" direction="vertical" className="w-full" size={16}>
              <div className="relative">
                <div className="absolute top-[64px] left-[43px]">
                  <Image preview={false} src={qrcode} width={122} height={122} />
                </div>
                <Image preview={false} src={phoneFrame} width={206} height={206} />
              </div>
              <span className="text-[#000000D9] text-center">
                {intl.formatMessage({ id: 'home.mobile.description' })}
              </span>
            </Space>
          </ProCard>
        </Space>
      </div>
    </PageContainer>
  );
};

export default withKeepAlive(Home);
