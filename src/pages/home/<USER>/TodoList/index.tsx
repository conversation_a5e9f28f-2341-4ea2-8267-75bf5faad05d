import { TimeFormat } from '@/components/common/TimeFormat';
import LeftTitle from '@/components/LeftTitle';
import CompleteTodo from '@/pages/system/todo/components/CompleteTodo';
import CreateTodo from '@/pages/system/todo/components/CreateTodo';
import { cancelTodo, queryTodoList } from '@/pages/system/todo/services';
import { StatusEnum, StatusEnumOptions } from '@/pages/system/todo/types/todo.enum';
import { RightOutlined } from '@ant-design/icons';
import { ProCard } from '@ant-design/pro-components';
import { history, useIntl, useModel } from '@umijs/max';
import { useRequest } from 'ahooks';
import { Divider, Flex, Popconfirm, Tag } from 'antd';
import { useState } from 'react';


const TodoList = () => {
  const [createTodoVisible, setCreateTodoVisible] = useState(false);
  const [editingTodo, setEditingTodo] = useState(null);
  const [completeTodoVisible, setCompleteTodoVisible] = useState(false);
  const [completingTodo, setCompletingTodo] = useState(null);
  const intl = useIntl();
  const t = (id: string, ...rest) => intl.formatMessage({ id }, ...rest);
  const { initialState } = useModel('@@initialState');
  const { currentUser } = initialState;

  const { data: result, run: refresh } = useRequest(() => queryTodoList({
    pageSize: 5,
  }));


  return (
    <>
      <ProCard headerBordered={false} bordered={false}>
        <Flex justify="space-between" align="center" className="mb-[16px]">
          <LeftTitle title={intl.formatMessage({ id: 'home.todo.title' })} />
          <span
            className="cursor-pointer"
            onClick={() => {
              history.push('/system/todo');
            }}
          >
            <span className="text-[#00000099]">{intl.formatMessage({ id: 'home.todo.allTasks' })}</span>
            <RightOutlined width={8} height={12} className="text-[#00000073] ml-2" />
          </span>
        </Flex>
        {
          (result?.data ?? []).map((record) => (
            <div key={record.id} className='mt-2'>
              <div>
                <span className='text-lg font-semibold'>
                  <TimeFormat time={record.createTime} showTime />
                </span>
                <Tag
                  className='ml-2'
                  style={{
                    verticalAlign: '2px'
                  }}
                  color={
                    StatusEnumOptions[record.status as StatusEnum].color
                  }>{
                    StatusEnumOptions[record.status as StatusEnum].text
                  }</Tag>
              </div>
              <div className='text-gray-400 text-[12px]'>
                {t('system.todo.creator')}：{record.creator}
                <Divider type="vertical" />
                {t('system.todo.todoPerson')}：{record.todoPersonName}
              </div>
              <div className='my-1 line-clamp-4 break-words'>
                {record.taskDesc}
              </div>
              {
                record.completionTime && <div className='text-gray-400 text-[12px]'>
                  {t('system.todo.completionTime')}: <TimeFormat time={record.completionTime} showTime />
                </div>
              }
              {
                <div className='flex gap-2'>
                  {
                    record.status == StatusEnum.NotCompleted && currentUser.accountId === record.createPerson && <a
                      key="edit"
                      onClick={() => {
                        setEditingTodo(record);
                        setCreateTodoVisible(true);
                      }}
                    >
                      {t('common.button.edit')}
                    </a>
                  }
                  {
                    record.status == StatusEnum.NotCompleted && currentUser.accountId === record.todoPerson && <a
                      key="complete"
                      onClick={() => {
                        setCompletingTodo(record);
                        setCompleteTodoVisible(true);
                      }}
                    >
                      {t('system.todo.button.complete')}
                    </a>
                  }
                  {
                    record.status !== StatusEnum.                    record.status == StatusEnum.NotCompleted && currentUser.accountId === record.todoPerson && <a
 &&
                    <Popconfirm key="cancel" title={
                      t('tip.confirm.action', { action: t('common.button.cancel') })
                    } onConfirm={async () => {
                      await cancelTodo({ id: record.id });
                      refresh();
                    }}>
                      <a
                        key="cancel"
                      >
                        {t('common.button.cancel')}
                      </a>
                    </Popconfirm>
                  }
                </div>
              }
            </div >
          ))
        }

      </ProCard >
      <CreateTodo
        open={createTodoVisible}
        onCancel={() => {
          setCreateTodoVisible(false);
          setEditingTodo(null);
        }}
        onOk={() => {
          setCreateTodoVisible(false);
          setEditingTodo(null);
          refresh();
        }}
        initialValues={editingTodo}
      />
      <CompleteTodo
        open={completeTodoVisible}
        onCancel={() => {
          setCompleteTodoVisible(false);
          setCompletingTodo(null);
        }}
        onOk={() => {
          setCompleteTodoVisible(false);
          setCompletingTodo(null);
          refresh();
        }}
        todo={completingTodo}
      />
    </>

  );
};

export default TodoList;
