import { accountListQuerySimple } from '@/pages/personnel/user/services';
import { ModalForm, ProFormSelect, ProFormText, ProFormTextArea } from '@ant-design/pro-components';
import { useIntl } from '@umijs/max';
import { Form } from 'antd';
import { createTodo, updateTodo } from '../services';

const CreateTodo = ({ open, onCancel, onOk, initialValues }) => {
  const [form] = Form.useForm();
  const intl = useIntl();
  const t = (id: string, ...rest) => intl.formatMessage({ id }, ...rest);

  const onFinish = async (values) => {
    if (initialValues) {
      await updateTodo({ ...initialValues, ...values });
    } else {
      await createTodo(values);
    }
    onOk();
  };

  return (
    <ModalForm
      title={initialValues ? t('system.todo.edit') : t('system.todo.create')}
      open={open}
      onOpenChange={(visible) => {
        if (!visible) {
          onCancel();
        }
      }}
      form={form}
      onFinish={onFinish}
      initialValues={initialValues}
    >
      <ProFormTextArea name="taskDesc" label={t('system.todo.taskDesc')} rules={[{ required: true }]}
        fieldProps={{
          showCount: true,
          maxLength: 100,
        }}
      />
      <ProFormSelect
        name="todoPerson" label={t('system.todo.todoPerson')} rules={[{ required: true }]}
        fieldProps={{ showSearch: true }}
        onChange={(value, option) => {
          form.setFieldsValue({
            todoPersonName: option.label,
          })
        }}
        request={async () => {
          const data = await accountListQuerySimple({});
          return data?.map(({ id, name }) => ({
            value: id,
            label: name,
          }));
        }}
      />
      <ProFormText name="todoPersonName" hidden />
    </ModalForm>
  );
};

export default CreateTodo;
