export interface ReturnLineList {
  /**
   * 品牌id
   */
  brandId?: string;
  /**
   * 品牌
   */
  brandName?: string;
  /**
   * 品牌件号
   */
  brandPartNo?: string;
  /**
   * 品牌件号
   */
  brandPartNoList?: string[];
  /**
   * 品类id
   */
  categoryId?: string;
  /**
   * 分类
   */
  categoryName?: string;
  /**
   * ETC号
   */
  etcNo?: string;
  /**
   * ETC订单商品id
   */
  etcSkuId?: string;
  /**
   * 明细id
   */
  id?: string;
  /**
   * 商品编码
   */
  itemId?: string;
  /**
   * 商品编码
   */
  itemSn?: string;
  /**
   * 库位
   */
  locationCode?: string;
  /**
   * 退货数量
   */
  num?: number;
  /**
   * oe号
   */
  oe?: string;
  /**
   * oe号
   */
  oeList?: string[];
  /**
   * 退货订单号
   */
  orderNo?: string;
  /**
   * 出库数量
   */
  outboundQuantity?: number;
  /**
   * 商品退货单价
   */
  price?: number;
  /**
   * 采购单号
   */
  purchaseOrderNo?: string;
  /**
   * 退货原因
   */
  reason?: string;
  /**
   * 商品id
   */
  skuId?: string;
  /**
   * 商品名称
   */
  skuName?: string;
  /**
   * 退货商品小记，单价数量
   */
  sumPrice?: number;
  /**
   * 商品行小计金额(含税)
   */
  taxationAmount?: number;
  /**
   * 单位
   */
  unit?: string;
}