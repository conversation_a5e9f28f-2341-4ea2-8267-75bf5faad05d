import AuthButton from '@/components/common/AuthButton';
import type { ProColumns } from '@ant-design/pro-components';
import { Popconfirm, Space, Tag } from 'antd';
import { postStatusOptions, setStatusValue, statusAttribute } from '../types/PostStatus';
import type { PostEntity } from '../types/post.entity';

export interface PostListTableColumnsProps {
  handleDeleteItem: (id: string, status: string) => void;
  handleUpdateItem: (id: string) => void;
  intl: any;
}

export const PostListTableColumns = (props: PostListTableColumnsProps) =>
  [
    {
      title: props.intl.formatMessage({ id: 'common.column.index' }),
      valueType: 'index',
      width: 40,
    },
    {
      title: props.intl.formatMessage({ id: 'system.user.list.employeeId' }),
      dataIndex: 'id',
      key: 'id',
      search: false,
      width: 80,
    },
    {
      title: props.intl.formatMessage({ id: 'system.user.list.employeeName' }),
      dataIndex: 'name',
      key: 'name',
      search: true,
      width: 80,
      ellipsis: true,
      render: (text, record) => {
        return (
          <Space>
            <span>{text}</span>
            {record.type == '0' && (
              <Tag color="blue">
                {props.intl.formatMessage({ id: 'system.user.list.mainAccount' })}
              </Tag>
            )}
          </Space>
        );
      },
    },
    {
      title: props.intl.formatMessage({ id: 'system.user.list.gender' }),
      dataIndex: 'gender',
      key: 'gender',
      search: false,
      width: 60,
    },
    {
      title: props.intl.formatMessage({ id: 'system.user.list.phone' }),
      dataIndex: 'phone',
      key: 'phone',
      search: true,
      width: 100,
    },
    {
      title: props.intl.formatMessage({ id: 'system.user.list.email' }),
      dataIndex: 'email',
      key: 'email',
      search: true,
      width: 100,
    },
    {
      title: props.intl.formatMessage({ id: 'system.user.list.store' }),
      dataIndex: 'storeNames',
      key: 'storeNames',
      search: true,
      width: 120,
      ellipsis: true,
    },
    {
      title: props.intl.formatMessage({ id: 'system.user.list.category' }),
      dataIndex: 'category',
      key: 'category',
      search: true,
      width: 80,
    },
    {
      title: props.intl.formatMessage({ id: 'system.user.list.position' }),
      dataIndex: 'position',
      key: 'position',
      search: true,
      width: 80,
    },
    {
      title: props.intl.formatMessage({ id: 'system.user.list.role' }),
      dataIndex: 'roleNames',
      key: 'roleNames',
      width: 80,
      search: false,
      ellipsis: true,
    },
    {
      title: props.intl.formatMessage({ id: 'system.user.list.hireState' }),
      dataIndex: 'hireState',
      key: 'hireState',
      search: false,
      width: 80,
    },
    {
      title: props.intl.formatMessage({ id: 'system.user.list.remark' }),
      dataIndex: 'remark',
      key: 'remark',
      search: false,
      width: 80,
    },
    {
      title: props.intl.formatMessage({ id: 'common.column.createTime' }),
      dataIndex: 'createTime',
      key: 'createTime',
      search: false,
      width: 160,
    },
    {
      title: props.intl.formatMessage({ id: 'common.column.status' }),
      dataIndex: 'status',
      key: 'status',
      search: false,
      width: 60,
      valueEnum: postStatusOptions,
    },
    {
      title: props.intl.formatMessage({ id: 'common.column.operation' }),
      key: 'operation',
      search: false,
      width: 80,
      fixed: 'right',
      render: (_, record: PostEntity) => (
        <Space>
          <AuthButton
            authority="editUser"
            isHref
            onClick={() => props.handleUpdateItem(record.id ?? '')}
          >
            {props.intl.formatMessage({ id: 'common.button.edit' })}
          </AuthButton>
          {record.type != '0' && (
            <Popconfirm
              title={props.intl.formatMessage(
                { id: 'system.user.list.confirm.enableDisable' },
                { action: props.intl.formatMessage({ id: statusAttribute[record.status ?? ''] }) },
              )}
              onConfirm={() =>
                props.handleDeleteItem(record.id ?? '', setStatusValue[record.status ?? ''])
              }
            >
              <AuthButton authority="enableOrDisableUser" isHref>
                {props.intl.formatMessage({ id: statusAttribute[record.status ?? ''] })}
              </AuthButton>
            </Popconfirm>
          )}
        </Space>
      ),
    },
  ] as ProColumns<PostEntity>[];
