import { ProForm } from '@ant-design/pro-components';
import React, { useState } from 'react';
import { EditableProTable } from '@ant-design/pro-table';

export interface FileUploadListProps {
  title?: React.ReactNode;
}

export default function FileUploadList(props: FileUploadListProps) {
  const { title } = props;
  const [editableKeys, setEditableRowKeys] = useState<React.Key[]>([]);
  const [dataSource, setDataSource] = useState([]);

  const columns = [
    {
      title: '序号',
      width: 60,
      dataIndex: 'index',
      valueType: 'index',
    },
    {
      title: '文件类型',
      dataIndex: 'name',
      formItemProps: {
        rules: [{ required: true, message: '请输入文件类型' }],
      },
    },
    {
      title: '文件',
      dataIndex: 'url',
      formItemProps: {
        rules: [{ required: true, message: '请上传文件' }],
      },
    },
    {
      title: '操作',
      valueType: 'option',
      width: 120,
      render: (text: any, record: any, _: any, action: any) => [
        <a
          key="editable"
          onClick={() => {
            action?.startEditable?.(record.id);
          }}
        >
          编辑
        </a>,
        <a
          key="delete"
          onClick={() => {
            setDataSource(dataSource.filter((item) => item.id !== record.id));
          }}
        >
          删除
        </a>,
      ],
    },
  ];
  return (
    <div>
      <ProForm.Item label={<div className="font-medium">{title}</div>} name="images">
        <EditableProTable
          rowKey="id"
          ghost={true}
          value={dataSource}
          // @ts-ignore
          onChange={setDataSource}
          columns={columns}
          recordCreatorProps={{
            newRecordType: 'dataSource',
            position: 'bottom',
            record: () => ({
              id: Date.now(),
            }),
          }}
          editable={{
            type: 'multiple',
            editableKeys,
            onChange: setEditableRowKeys,
          }}
        />
      </ProForm.Item>
    </div>
  );
}
