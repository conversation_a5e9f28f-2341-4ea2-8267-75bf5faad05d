import { WorkdayEnum } from './workday.enum';

export interface UserEntity {
  /**
   * account
   */
  account?: Account;
  /**
   * 员工银行信息
   */
  accountBankList?: AccountBankList[];
  /**
   * accountEmployment
   */
  accountEmployment?: AccountEmployment;
  /**
   * accountSensitive
   */
  accountSensitive?: AccountSensitive;
  /**
   * 员工年金信息
   */
  accountSuperannuationList?: AccountSuperannuationList[];
  /**
   * none
   */
  extRemark?: string;
  /**
   * none
   */
  firstName?: string;
  /**
   * none
   */
  lastName?: string;
  /**
   * none
   */
  memberId?: string;
  /**
   * none
   */
  memberName?: string;
  /**
   * none
   */
  operatorName?: string;
  /**
   * none
   */
  operatorNo?: string;
}

/**
 * account
 */
export interface Account {
  /**
   * 员工地址信息
   */
  addressList?: AddressList[];
  /**
   * 出生日期
   */
  birthDate?: string;
  /**
   * 邮箱
   */
  email?: string;
  /**
   * firstName
   */
  firstName?: string;
  /**
   * 性别
   */
  gender?: string;
  /**
   * 主键id
   */
  id?: string;
  /**
   * lastName
   */
  lastName?: string;
  /**
   * 零售商ID
   */
  memberId?: string;
  /**
   * 账户名
   */
  name?: string;
  /**
   * 操作人
   */
  operator?: string;
  /**
   * 手机号
   */
  phone?: string;
  /**
   * 职位D列表
   */
  positionIds?: string[];
  /**
   * 备注
   */
  remark?: string;
  /**
   * 角色ID列表
   */
  roleIds?: string[];
  /**
   * 门店ID列表
   */
  storeIds?: string[];
  /**
   * 类型：0主账户，5子账户
   */
  type?: string;
}

export interface AddressList {
  /**
   * 账户ID
   */
  accountId?: string;
  /**
   * 详细地址
   */
  address?: string;
  /**
   * 市编码
   */
  cityCode?: string;
  /**
   * 市名称
   */
  cityName?: string;
  /**
   * 主键
   */
  id?: string;
  /**
   * 区县编码
   */
  prefectureCode?: string;
  /**
   * 区县名称
   */
  prefectureName?: string;
  /**
   * 省编码
   */
  provinceCode?: string;
  /**
   * 省名称
   */
  provinceName?: string;
}

export interface AccountBankList {
  /**
   * 账户id
   */
  accountId?: string;
  /**
   * 账号名称
   */
  accountName?: string;
  /**
   * 银行账号
   */
  accountNumber?: string;
  /**
   * 银行行号
   */
  bankAccount?: string;
  /**
   * 主键
   */
  id?: string;
}

/**
 * accountEmployment
 */
export interface AccountEmployment {
  /**
   * 账户id
   */
  accountId?: string;
  /**
   * 员工工作时间
   */
  accountWorkingScheduleList?: AccountWorkingScheduleCmdList[];
  /**
   * 开始计算年假日期
   */
  accrualStartDate?: string;
  /**
   * 雇佣类别
   */
  category?: number;
  /**
   * 入职日期
   */
  hireDate?: string;
  /**
   * 雇佣状态
   */
  hireState?: number;
  /**
   * 时薪
   */
  hourlyPay?: string;
  /**
   * 主键
   */
  id?: string;
  /**
   * 图片
   */
  images?: AccountEmploymentImage[];
  /**
   * 实习结束日期
   */
  internshipEndDate?: string;
  /**
   * 实习开始日期
   */
  internshipStartDate?: string;
  /**
   * 关联的职位ID
   */
  positionIds?: string[];
  /**
   * 角色ID列表
   */
  roleIds?: string[];
  /**
   * 门店ID列表
   */
  storeIds?: string[];
  /**
   * 离职日期
   */
  terminationDate?: string;
  /**
   * 离职原因
   */
  terminationReason?: string;
}

export interface AccountWorkingScheduleCmdList {
  /**
   * 账户id
   */
  accountId?: string;
  /**
   * 主键
   */
  id?: string;
  /**
   * 工作时间
   */
  scheduledHours?: ScheduledHour[];
  /**
   * 周一到周日
   */
  scheduledWorkday?: WorkdayEnum;
  /**
   * 周一到周日
   */
  scheduledWorkdayDesc?: string;
  /**
   * 总工作时长
   */
  totalHours?: string;
}

export interface ScheduledHour {
  /**
   * endTime
   */
  endTime?: string;
  /**
   * startTime
   */
  startTime?: string;
}

export interface AccountEmploymentImage {
  /**
   * name
   */
  name?: string;
  /**
   * 地址
   */
  url?: string;
}

/**
 * accountSensitive
 */
export interface AccountSensitive {
  /**
   * ABN
   */
  abn?: string;
  /**
   * 账户id
   */
  accountId?: string;
  /**
   * 主键
   */
  id?: string;
  /**
   * 图片
   */
  images?: AccountSensitiveImage[];
  /**
   * 护照号
   */
  passportNumber?: string;
  /**
   * 税号
   */
  taxFileNumber?: string;
  /**
   * 签证号
   */
  visaNumber?: string;
}

export interface AccountSensitiveImage {
  /**
   * name
   */
  name?: string;
  /**
   * 地址
   */
  url?: string;
}

export interface AccountSuperannuationList {
  /**
   * 账户id
   */
  accountId?: string;
  /**
   * 员工年金账号
   */
  accountNumber?: string;
  /**
   * 年金所属公司
   */
  fundName?: string;
  /**
   * 主键
   */
  id?: string;
  /**
   * 年金公司号码
   */
  usi?: string;
}
