import { getCstList } from "@/pages/customer/list/services";
import { getOtherRelatedCompanyList } from "@/pages/finance/otherRelated/service";
import { querySupplierList } from "@/pages/purchase/supplier/services";
import { FormInstance, ProFormSelect, ProFormSelectProps, ProFormText } from "@ant-design/pro-components";
import { useEffect, useState } from "react";


// 与收支对象类型类型一致
export enum ObjectType {
  // 供应商
  Suppler = 3,
  // 客户
  Customer = 1,
  // 其他往来单位
  OtherCompany = 2,
}

interface ProFormObjectProps {
  label?: string;
  objects: ObjectType[];
  form: FormInstance;
  fieldsName: {
    fieldType: string;
    fieldName: string;
    fieldId: string;
  }
}
const ProFormObject = (props: ProFormObjectProps & ProFormSelectProps) => {
  const { objects, fieldsName, form, ...rest } = props;
  const { fieldType = 'objectType', fieldName = 'objectName', fieldId = 'objectId' } = fieldsName || {};

  const [objectType, setObjectType] = useState<ObjectType>(objects[0]);
  const [options, setOptions] = useState<{ value?: string; label?: string }[]>([]);
  useEffect(() => {
    setOptions([]);
    console.log(objectType);
    if (objectType === ObjectType.Customer) {
      getCstList({ cstStatus: 0 }).then((data) => {
        setOptions(data?.map(({ cstId, cstName }) => ({
          value: cstId,
          label: cstName,
        })));
      });
    }
    if (objectType === ObjectType.OtherCompany) {
      getOtherRelatedCompanyList().then((data) => {
        setOptions(data?.map(({ companyId, companyName }) => ({
          value: companyId,
          label: companyName,
        })));
      });
    }
    if (objectType === ObjectType.Suppler) {
      querySupplierList({}).then((data) => {
        setOptions(data?.map(({ supplierId, supplierName }) => ({
          value: supplierId,
          label: supplierName,
        })));
      });
    }
  }, [objectType]);


  return (
    <div className="flex">
      <div className="flex-1">
        <ProFormSelect
          width={'100%'}
          label={props.label}
          name={fieldType}
          options={[
            { label: '供应商', value: ObjectType.Suppler },
            { label: '客户', value: ObjectType.Customer },
            { label: '其他往来单位', value: ObjectType.OtherCompany },
          ].filter((item) => props.objects.includes(item.value))}
          formItemProps={{
            initialValue: objects?.[0],
          }}
          onChange={(value) => {
            setObjectType(value);
            props.form.setFieldsValue({
              [fieldId]: undefined,
              [fieldName]: undefined,
            });
          }}
          {...rest}
        />
      </div>
      <div className="flex-1">
        <ProFormSelect
          width={200}
          name={fieldId}
          label={props.label ? " " : ""}
          colon={false}
          showSearch
          options={options}
          onChange={(value, option) => {
            props.form.setFieldsValue({
              [fieldName]: option?.label,
            });
          }}
          disabled={props.disabled}
        />
        <ProFormText hidden name={fieldName} />
      </div>
    </div >
  )
}

export default ProFormObject;